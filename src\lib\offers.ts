import { supabase } from './supabase';
import {
  notify<PERSON>fferReceived,
  notifyOfferAccepted,
  notifyOfferRejected,
} from './notifications';
import { upsertOfferChatPermission } from './chat-permissions';

export interface DirectOfferInsert {
  influencer_id: string;
  title: string;
  description: string;
  budget: number;
  content_types: string[];
  platforms: string[];
  deadline?: string | null;
  requirements?: string | null;
  offer_type?: 'custom' | 'package_order';
  package_id?: number | null;
}

export interface DirectOffer {
  id: string;
  business_id: string;
  influencer_id: string;
  title: string;
  description: string;
  budget: number;
  content_types: string[];
  platforms: string[];
  deadline?: string | null;
  requirements?: string | null;
  status: 'pending' | 'accepted' | 'rejected' | 'completed' | 'cancelled';
  offer_type: 'custom' | 'package_order';
  package_id?: number | null;
  created_at: string;
  updated_at: string;
  responded_at?: string | null;
}

export interface DirectOfferWithDetails extends DirectOffer {
  businesses?: {
    id: string;
    company_name: string;
    industry?: string;
    profiles: {
      avatar_url?: string;
    };
  };
  influencer: {
    id: string;
    full_name: string | null;
    username: string;
    avatar_url?: string | null;
  };
}

// Kreiranje direktne ponude
export async function createDirectOffer(offerData: DirectOfferInsert) {
  try {
    // Prvo dobijamo trenutnog korisnika
    const { data: user } = await supabase.auth.getUser();
    if (!user.user) {
      throw new Error('Korisnik nije prijavljen');
    }

    // Proveravamo da li je korisnik business tip
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('id, user_type')
      .eq('id', user.user.id)
      .single();

    if (profileError || !profile) {
      throw new Error('Profil nije pronađen');
    }

    if (profile.user_type !== 'business') {
      throw new Error('Samo business korisnici mogu kreirati ponude');
    }

    console.log('Creating direct offer with data:', {
      business_id: profile.id,
      ...offerData,
    });

    const { data, error } = await supabase
      .from('direct_offers')
      .insert({
        business_id: profile.id,
        ...offerData,
      })
      .select('*')
      .single();

    console.log('Direct offer creation result:', { data, error });

    if (error) {
      console.error('Direct offer creation error:', error);
      throw new Error(`Greška pri kreiranju ponude: ${error.message}`);
    }

    // Kreiraj notifikaciju za influencera
    if (data) {
      try {
        // Get business and influencer data separately for notification
        const { data: businessProfile } = await supabase
          .from('profiles')
          .select('full_name')
          .eq('id', profile.id)
          .single();

        const { data: influencerProfile } = await supabase
          .from('profiles')
          .select('id')
          .eq('id', data.influencer_id)
          .single();

        if (businessProfile && influencerProfile) {
          await notifyOfferReceived(
            influencerProfile.id,
            businessProfile.full_name || 'Business',
            data.title,
            data.id
          );
        }
      } catch (notificationError) {
        console.error('Error creating notification:', notificationError);
        // Ne prekidamo proces ako notifikacija ne uspije
      }
    }

    return { data, error };
  } catch (error) {
    console.error('Error creating direct offer:', error);
    return { data: null, error };
  }
}

// Kreiranje narudžbe paketa
export async function createPackageOrder(packageData: {
  influencer_id: string;
  package_id: number;
  package_name: string;
  price: number;
  platform_name: string;
  content_type_name: string;
  requirements?: string;
  message?: string;
}) {
  try {
    const { data: user } = await supabase.auth.getUser();
    if (!user.user) throw new Error('User not authenticated');

    // Kreiraj narudžbu kao direktnu ponudu sa tipom 'package_order'
    const offerData: DirectOfferInsert = {
      influencer_id: packageData.influencer_id,
      title: `Narudžba: ${packageData.package_name}`,
      description: packageData.message || `Narudžba paketa "${packageData.package_name}" na ${packageData.platform_name} platformi`,
      budget: packageData.price,
      content_types: [packageData.content_type_name],
      platforms: [packageData.platform_name],
      offer_type: 'package_order',
      package_id: packageData.package_id,
      requirements: packageData.requirements || `Automatski kreirana narudžba za paket: ${packageData.package_name}`,
    };

    const { data, error } = await supabase
      .from('direct_offers')
      .insert({
        business_id: user.user.id,
        ...offerData,
      })
      .select()
      .single();

    if (error) throw error;

    // Dobij business profil za notifikaciju
    const { data: businessProfile } = await supabase
      .from('profiles')
      .select('full_name')
      .eq('id', user.user.id)
      .single();

    // Pošalji notifikaciju influenceru
    await notifyOfferReceived(
      data.influencer_id,
      businessProfile?.full_name || 'Business',
      data.title,
      data.id
    );

    // Kreiraj chat permission
    await upsertOfferChatPermission(
      user.user.id,
      data.influencer_id,
      data.id
    );

    return { data, error: null };
  } catch (error) {
    console.error('Error creating package order:', error);
    return { data: null, error };
  }
}

// Dobijanje ponuda za biznis
export async function getBusinessOffers(status?: string) {
  try {
    const { data: user } = await supabase.auth.getUser();
    if (!user.user) throw new Error('User not authenticated');

    // Prvo dobijamo ponude za ovaj biznis
    let offersQuery = supabase
      .from('direct_offers')
      .select('*')
      .eq('business_id', user.user.id)
      .order('created_at', { ascending: false });

    if (status) {
      offersQuery = offersQuery.eq('status', status);
    }

    const { data: offers, error: offersError } = await offersQuery;
    if (offersError) throw offersError;

    if (!offers || offers.length === 0) {
      return { data: [], error: null };
    }

    // Zatim dobijamo profile influencera
    const influencerIds = offers.map(offer => offer.influencer_id);
    const { data: profiles, error: profilesError } = await supabase
      .from('profiles')
      .select('id, username, full_name, avatar_url')
      .in('id', influencerIds);

    if (profilesError) throw profilesError;

    // Kombinujemo podatke
    const offersWithDetails = offers.map(offer => ({
      ...offer,
      influencer: profiles?.find(p => p.id === offer.influencer_id) || {
        id: offer.influencer_id,
        username: 'Unknown',
        full_name: 'Unknown User',
        avatar_url: null,
      },
    }));

    return { data: offersWithDetails as DirectOfferWithDetails[], error: null };
  } catch (error) {
    console.error('Error fetching business offers:', error);
    return { data: null, error };
  }
}

// Dobijanje ponuda za influencera
export async function getInfluencerOffers(status?: string) {
  try {
    const { data: user } = await supabase.auth.getUser();
    if (!user.user) throw new Error('User not authenticated');

    // Prvo dobijamo influencer_id iz profiles tabele
    const { data: profileData, error: profileError } = await supabase
      .from('profiles')
      .select('id')
      .eq('id', user.user.id)
      .eq('user_type', 'influencer')
      .single();

    if (profileError || !profileData) {
      console.log('Influencer profile not found for user:', user.user.id);
      return { data: [], error: null };
    }

    let query = supabase
      .from('direct_offers')
      .select(
        `
        *,
        businesses!inner(
          id,
          company_name,
          industry,
          profiles!inner(
            avatar_url
          )
        )
      `
      )
      .eq('influencer_id', profileData.id)
      .order('created_at', { ascending: false });

    if (status) {
      query = query.eq('status', status);
    }

    const { data, error } = await query;

    if (error) throw error;

    return { data: data as DirectOfferWithDetails[], error: null };
  } catch (error) {
    console.error('Error fetching influencer offers:', error);
    return { data: null, error };
  }
}

// Ažuriranje statusa ponude
export async function updateOfferStatus(
  offerId: string,
  status: 'accepted' | 'rejected',
  influencerResponse?: string
) {
  try {
    const updateData: any = {
      status,
      updated_at: new Date().toISOString(),
      responded_at: new Date().toISOString(),
    };

    const { data, error } = await supabase
      .from('direct_offers')
      .update(updateData)
      .eq('id', offerId)
      .select('*')
      .single();

    // Kreiraj notifikaciju za biznis i chat dozvole
    if (data && !error) {
      try {
        // Dobij informacije o biznis profilu za notifikacije
        const { data: businessProfile } = await supabase
          .from('profiles')
          .select('id, username')
          .eq('id', data.business_id)
          .single();

        if (status === 'accepted') {
          if (businessProfile) {
            await notifyOfferAccepted(
              businessProfile.id,
              businessProfile.username,
              data.title,
              data.id
            );
          }

          // Kreiraj chat dozvolu kada se ponuda prihvati
          // Influencer je već odobrio prihvatanjem ponude
          await upsertOfferChatPermission(
            data.business_id,
            data.influencer_id,
            data.id,
            false, // business_approved - čeka da biznis odobri
            true // influencer_approved - influencer je odobrio prihvatanjem
          );
        } else if (status === 'rejected') {
          if (businessProfile) {
            await notifyOfferRejected(
              businessProfile.id,
              businessProfile.username,
              data.title,
              data.id
            );
          }
        }
      } catch (notificationError) {
        console.error(
          'Error creating notification or chat permission:',
          notificationError
        );
        // Ne prekidamo proces ako notifikacija ne uspije
      }
    }

    return { data, error };
  } catch (error) {
    console.error('Error updating offer status:', error);
    return { data: null, error };
  }
}

// Dobijanje pojedinačne ponude sa detaljima
export async function getDirectOffer(offerId: string) {
  try {
    // Prvo dobijamo osnovne podatke o ponudi
    const { data: offer, error: offerError } = await supabase
      .from('direct_offers')
      .select('*')
      .eq('id', offerId)
      .single();

    if (offerError) throw offerError;
    if (!offer) throw new Error('Offer not found');

    // Dobijamo business podatke
    const { data: business, error: businessError } = await supabase
      .from('businesses')
      .select('id, company_name, industry')
      .eq('id', offer.business_id)
      .single();

    if (businessError) throw businessError;

    // Dobijamo business profile podatke
    const { data: businessProfile, error: businessProfileError } =
      await supabase
        .from('profiles')
        .select('avatar_url')
        .eq('id', offer.business_id)
        .single();

    if (businessProfileError) throw businessProfileError;

    // Dobijamo influencer profile podatke
    const { data: influencerProfile, error: influencerError } = await supabase
      .from('profiles')
      .select('id, username, full_name, avatar_url')
      .eq('id', offer.influencer_id)
      .single();

    if (influencerError) throw influencerError;

    // Kombinujemo sve podatke
    const offerWithDetails = {
      ...offer,
      businesses: {
        ...business,
        profiles: businessProfile,
      },
      influencer: influencerProfile,
    };

    return { data: offerWithDetails as DirectOfferWithDetails, error: null };
  } catch (error) {
    console.error('Error fetching direct offer:', error);
    return { data: null, error };
  }
}

// Provjera da li korisnik može pristupiti ponudi
export async function canAccessOffer(offerId: string) {
  try {
    const { data: user } = await supabase.auth.getUser();
    if (!user.user) return false;

    const { data, error } = await supabase
      .from('direct_offers')
      .select(
        `
        id,
        businesses!inner(user_id),
        influencers!inner(user_id)
      `
      )
      .eq('id', offerId)
      .single();

    if (error || !data) return false;

    // Provjeri da li je korisnik vlasnik biznisa ili influencer
    return (
      data.businesses.user_id === user.user.id ||
      data.influencers.user_id === user.user.id
    );
  } catch (error) {
    console.error('Error checking offer access:', error);
    return false;
  }
}

// Statistike za dashboard
export async function getOfferStats() {
  try {
    const { data: user } = await supabase.auth.getUser();
    if (!user.user) throw new Error('User not authenticated');

    // Provjeri tip korisnika
    const { data: businessData } = await supabase
      .from('businesses')
      .select('id')
      .eq('user_id', user.user.id)
      .single();

    const { data: influencerData } = await supabase
      .from('influencers')
      .select('id')
      .eq('user_id', user.user.id)
      .single();

    if (businessData) {
      // Statistike za biznis
      const { data: stats, error } = await supabase
        .from('direct_offers')
        .select('status')
        .eq('business_id', businessData.id);

      if (error) throw error;

      const total = stats?.length || 0;
      const pending = stats?.filter(s => s.status === 'pending').length || 0;
      const accepted = stats?.filter(s => s.status === 'accepted').length || 0;
      const rejected = stats?.filter(s => s.status === 'rejected').length || 0;

      return {
        data: { total, pending, accepted, rejected },
        error: null,
        userType: 'business',
      };
    } else if (influencerData) {
      // Statistike za influencera
      const { data: stats, error } = await supabase
        .from('direct_offers')
        .select('status')
        .eq('influencer_id', influencerData.id);

      if (error) throw error;

      const total = stats?.length || 0;
      const pending = stats?.filter(s => s.status === 'pending').length || 0;
      const accepted = stats?.filter(s => s.status === 'accepted').length || 0;
      const rejected = stats?.filter(s => s.status === 'rejected').length || 0;

      return {
        data: { total, pending, accepted, rejected },
        error: null,
        userType: 'influencer',
      };
    }

    return { data: null, error: 'User type not found', userType: null };
  } catch (error) {
    console.error('Error fetching offer stats:', error);
    return { data: null, error, userType: null };
  }
}
