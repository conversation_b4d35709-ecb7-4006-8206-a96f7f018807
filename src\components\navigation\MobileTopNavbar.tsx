'use client';

import { useState } from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
  DropdownMenuLabel,
} from '@/components/ui/dropdown-menu';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  Bell,
  ChevronDown,
  User,
  Settings,
  DollarSign,
  LogOut,
  CheckCircle,
  Send,
  FileText,
  Users,
  Check,
  CheckCheck,
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';
import {
  getUserNotifications,
  markNotificationAsRead,
  markAllNotificationsAsRead,
  getUnreadNotificationCount,
  type Notification,
} from '@/lib/notifications';
import { formatDistanceToNow } from 'date-fns';
import { hr } from 'date-fns/locale';
import { toast } from 'sonner';
import { useEffect } from 'react';

interface MobileTopNavbarProps {
  userType: 'influencer' | 'business';
  profile: any;
}

interface ProfileMenuItem {
  name: string;
  href: string;
  icon: React.ComponentType<{ className?: string }>;
  description?: string;
}

export function MobileTopNavbar({ userType, profile }: MobileTopNavbarProps) {
  const { user, signOut } = useAuth();
  const router = useRouter();
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [isLoading, setIsLoading] = useState(false);


  // Profile dropdown items for business users
  const businessProfileItems: ProfileMenuItem[] = [
    {
      name: 'Moj račun',
      href: '/dashboard/biznis/account',
      icon: User,
      description: 'Podaci o firmi i sigurnost',
    },
    {
      name: 'Postavke profila',
      href: '/dashboard/biznis/profile',
      icon: Settings,
      description: 'Javni profil firme',
    },
    {
      name: 'Moje ponude',
      href: '/dashboard/biznis/offers',
      icon: Send,
      description: 'Direktne ponude influencerima',
    },
    {
      name: 'Završeni poslovi',
      href: '/dashboard/biznis/zavrseni-poslovi',
      icon: CheckCircle,
      description: 'Praćenje završenih poslova',
    },
  ];

  // Profile dropdown items for influencer users
  const influencerProfileItems: ProfileMenuItem[] = [
    {
      name: 'Moj račun',
      href: '/dashboard/influencer/account',
      icon: User,
      description: 'Lični podaci i sigurnost',
    },
    {
      name: 'Postavke profila',
      href: '/dashboard/influencer/profile',
      icon: Settings,
      description: 'Javni profil i cijene',
    },
    {
      name: 'Zarada',
      href: '/dashboard/influencer/earnings',
      icon: DollarSign,
      description: 'Pregled zarade i isplate',
    },
    {
      name: 'Završeni poslovi',
      href: '/dashboard/influencer/zavrseni-poslovi',
      icon: CheckCircle,
      description: 'Praćenje završenih poslova',
    },
  ];

  const profileItems = userType === 'influencer' ? influencerProfileItems : businessProfileItems;

  useEffect(() => {
    if (user) {
      loadNotifications();
      loadUnreadCount();
    }
  }, [user]);

  const loadNotifications = async () => {
    if (!user) return;

    setIsLoading(true);
    try {
      const { data, error } = await getUserNotifications(user.id, 20);
      if (error) {
        console.error('Error loading notifications:', error);
      } else {
        setNotifications(data || []);
      }
    } catch (error) {
      console.error('Error loading notifications:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const loadUnreadCount = async () => {
    if (!user) return;

    try {
      const { count, error } = await getUnreadNotificationCount(user.id);
      if (error) {
        console.error('Error loading unread count:', error);
      } else {
        setUnreadCount(count);
      }
    } catch (error) {
      console.error('Error loading unread count:', error);
    }
  };

  const handleMarkAsRead = async (notificationId: string) => {
    try {
      const { error } = await markNotificationAsRead(notificationId);
      if (error) {
        toast.error('Greška pri označavanju notifikacije');
      } else {
        setNotifications(prev =>
          prev.map(n => (n.id === notificationId ? { ...n, read: true } : n))
        );
        setUnreadCount(prev => Math.max(0, prev - 1));
      }
    } catch (error) {
      console.error('Error marking notification as read:', error);
      toast.error('Greška pri označavanju notifikacije');
    }
  };

  const handleMarkAllAsRead = async () => {
    if (!user) return;

    try {
      const { error } = await markAllNotificationsAsRead(user.id);
      if (error) {
        toast.error('Greška pri označavanju notifikacija');
      } else {
        setNotifications(prev => prev.map(n => ({ ...n, read: true })));
        setUnreadCount(0);
        toast.success('Sve notifikacije označene kao pročitane');
      }
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
      toast.error('Greška pri označavanju notifikacija');
    }
  };

  const handleSignOut = async () => {
    try {
      await signOut();
      router.push('/');
    } catch (error) {
      console.error('Error signing out:', error);
    }
  };

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'offer_received':
      case 'offer_accepted':
      case 'offer_rejected':
        return <FileText className="h-4 w-4" />;
      case 'campaign_application':
      case 'campaign_accepted':
      case 'campaign_rejected':
        return <FileText className="h-4 w-4" />;
      case 'job_completion_submitted':
      case 'job_completion_approved':
      case 'job_completion_rejected':
        return <CheckCheck className="h-4 w-4" />;
      case 'message_received':
        return <Bell className="h-4 w-4" />;
      case 'payment_received':
        return <DollarSign className="h-4 w-4" />;
      default:
        return <Bell className="h-4 w-4" />;
    }
  };

  const getNotificationLink = (notification: Notification) => {
    switch (notification.type) {
      case 'offer_received':
      case 'offer_accepted':
      case 'offer_rejected':
        if (notification.data.offer_id) {
          return `/dashboard/influencer/offers/${notification.data.offer_id}`;
        }
        return '/dashboard/influencer/offers';
      case 'job_completion_submitted':
        if (notification.data.direct_offer_id) {
          return `/dashboard/biznis/offers/${notification.data.direct_offer_id}`;
        }
        return '/dashboard/biznis/offers';
      case 'job_completion_approved':
      case 'job_completion_rejected':
        if (notification.data.job_completion_id) {
          return '/dashboard/job-completions';
        }
        return '/dashboard';
      case 'message_received':
        if (notification.data.conversation_id) {
          return `/dashboard/chat/${notification.data.conversation_id}`;
        }
        return '/dashboard/chat';
      default:
        return '/dashboard';
    }
  };

  return (
    <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 md:hidden">
      <div className="flex h-14 items-center justify-between px-4">
        {/* Logo */}
        <Link href="/" className="flex items-center space-x-2">
          <div className="w-7 h-7 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
            <span className="text-white font-bold text-sm">🔗</span>
          </div>
          <h1 className="text-lg font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
            INFLUEXUS
          </h1>
        </Link>

        {/* Right side - Notifications & Profile */}
        <div className="flex items-center space-x-2">
          {/* Notifications */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="relative p-2">
                <Bell className="h-5 w-5" />
                {unreadCount > 0 && (
                  <Badge
                    variant="destructive"
                    className="absolute -top-1 -right-1 h-5 w-5 flex items-center justify-center p-0 text-xs"
                  >
                    {unreadCount > 99 ? '99+' : unreadCount}
                  </Badge>
                )}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-80">
              <DropdownMenuLabel className="flex items-center justify-between p-4">
                <h3 className="font-semibold">Notifikacije</h3>
                {unreadCount > 0 && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleMarkAllAsRead}
                    className="text-xs"
                  >
                    <CheckCheck className="h-3 w-3 mr-1" />
                    Označi sve
                  </Button>
                )}
              </DropdownMenuLabel>
              <DropdownMenuSeparator />

              <ScrollArea className="h-96">
                {isLoading ? (
                  <div className="flex items-center justify-center p-8">
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
                  </div>
                ) : notifications.length === 0 ? (
                  <div className="flex flex-col items-center justify-center p-8 text-center">
                    <Bell className="h-8 w-8 text-muted-foreground mb-2" />
                    <p className="text-sm text-muted-foreground">
                      Nema novih notifikacija
                    </p>
                  </div>
                ) : (
                  <div className="space-y-1">
                    {notifications.map(notification => (
                      <div
                        key={notification.id}
                        className={`p-3 hover:bg-muted/50 transition-colors border-l-2 ${
                          notification.read ? 'border-transparent' : 'border-primary'
                        }`}
                      >
                        <Link
                          href={getNotificationLink(notification)}
                          onClick={() => {
                            if (!notification.read) {
                              handleMarkAsRead(notification.id);
                            }
                          }}
                          className="block"
                        >
                          <div className="flex items-start gap-3">
                            <div className="flex-shrink-0 mt-0.5">
                              {getNotificationIcon(notification.type)}
                            </div>
                            <div className="flex-1 min-w-0">
                              <div className="flex items-start justify-between">
                                <h4
                                  className={`text-sm font-medium ${
                                    notification.read
                                      ? 'text-muted-foreground'
                                      : 'text-foreground'
                                  }`}
                                >
                                  {notification.title}
                                </h4>
                                {!notification.read && (
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={e => {
                                      e.preventDefault();
                                      e.stopPropagation();
                                      handleMarkAsRead(notification.id);
                                    }}
                                    className="h-6 w-6 p-0 ml-2"
                                  >
                                    <Check className="h-3 w-3" />
                                  </Button>
                                )}
                              </div>
                              <p
                                className={`text-xs mt-1 ${
                                  notification.read
                                    ? 'text-muted-foreground'
                                    : 'text-muted-foreground'
                                }`}
                              >
                                {notification.message}
                              </p>
                              <p className="text-xs text-muted-foreground mt-1">
                                {formatDistanceToNow(
                                  new Date(notification.created_at),
                                  {
                                    addSuffix: true,
                                    locale: hr,
                                  }
                                )}
                              </p>
                            </div>
                          </div>
                        </Link>
                      </div>
                    ))}
                  </div>
                )}
              </ScrollArea>

              {notifications.length > 0 && (
                <>
                  <DropdownMenuSeparator />
                  <div className="p-2">
                    <Button variant="ghost" size="sm" className="w-full" asChild>
                      <Link href="/dashboard/notifications">
                        Pogledaj sve notifikacije
                      </Link>
                    </Button>
                  </div>
                </>
              )}
            </DropdownMenuContent>
          </DropdownMenu>

          {/* Profile */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="flex items-center space-x-1 px-1 py-1">
                <Avatar className="h-7 w-7">
                  <AvatarImage src={profile?.avatar_url} alt="Profile" />
                  <AvatarFallback>
                    {profile?.full_name?.charAt(0) || profile?.username?.charAt(0) || 'U'}
                  </AvatarFallback>
                </Avatar>
                <ChevronDown className="h-3 w-3 text-muted-foreground" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-56">
              <div className="flex items-center space-x-2 p-2">
                <Avatar className="h-8 w-8">
                  <AvatarImage src={profile?.avatar_url} alt="Profile" />
                  <AvatarFallback>
                    {profile?.full_name?.charAt(0) || profile?.username?.charAt(0) || 'U'}
                  </AvatarFallback>
                </Avatar>
                <div className="flex flex-col space-y-1">
                  <p className="text-sm font-medium">
                    {profile?.full_name || profile?.username}
                  </p>
                  <p className="text-xs text-muted-foreground">
                    @{profile?.username}
                  </p>
                </div>
              </div>
              <DropdownMenuSeparator />
              {profileItems.map((item) => (
                <DropdownMenuItem key={item.name} asChild className="cursor-pointer">
                  <Link href={item.href}>
                    <item.icon className="mr-2 h-4 w-4" />
                    <span>{item.name}</span>
                  </Link>
                </DropdownMenuItem>
              ))}
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={handleSignOut} className="cursor-pointer text-red-600">
                <LogOut className="mr-2 h-4 w-4" />
                <span>Odjavi se</span>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </header>
  );
}
